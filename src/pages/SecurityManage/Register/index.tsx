import { AuditRegisterItem, GetRegisterList } from '@/services/securityManage';
import { useModel } from '@umijs/max';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Table,
} from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { RegisterItem, RegisterResponse } from './type';

const ROLES_OPTIONS = {
  sa: [
    { label: '公司 / 部门负责人', value: 'dept_head' },
    { label: '公司 / 部门管理员', value: 'dept_admin' },
    { label: '业务员', value: 'user' },
  ],
  dept_head: [
    { label: '公司 / 部门管理员', value: 'dept_admin' },
    { label: '业务员', value: 'user' },
  ],
  dept_admin: [{ label: '业务员', value: 'user' }],
};

export default function Register() {
  const { initialState } = useModel('@@initialState');
  const currentUserRole = initialState?.currentUser?.roles[0] ?? 'user';

  const [loading, setLoading] = useState(false);
  const [passModalVisible, setPassModalVisible] = useState(false);
  const [registerList, setRegisterList] = useState<RegisterItem[]>([]);
  const [tableType, setTableType] = useState('全部');

  const [form] = Form.useForm();

  const getRegisterList = useCallback(async () => {
    setLoading(true);
    try {
      const res: RegisterResponse = await GetRegisterList(tableType);
      if (res.code === 200001) {
        setRegisterList(res.data.list);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  }, [tableType]);

  const handleAudit = useCallback(
    async (id: string, status: boolean) => {
      setLoading(true);
      try {
        const res = await AuditRegisterItem({ id, pass: status });
        if (res.code === 200001) {
          message.success('操作成功');
          await getRegisterList();
          setPassModalVisible(false);
        } else {
          message.error(res.message);
        }
      } catch (error) {
        message.error(JSON.stringify(error));
      } finally {
        setLoading(false);
      }
    },
    [getRegisterList],
  );

  useEffect(() => {
    getRegisterList();
  }, [tableType, getRegisterList]);

  const columns = useMemo(
    () => [
      { title: '姓名', dataIndex: 'full_name' },
      { title: '手机号', dataIndex: 'mobile' },
      { title: '邮箱', dataIndex: 'email' },
      { title: '角色', dataIndex: 'roles' },
      { title: '状态', dataIndex: 'register_status' },
      {
        title: '操作',
        render: (record: RegisterItem) => {
          if (record.register_status !== '待审核') return '-';

          return (
            <Space size="small">
              <Button
                className="px-0"
                type="link"
                onClick={() => {
                  form.setFieldsValue(record);
                  setPassModalVisible(true);
                }}
              >
                通过
              </Button>
              <Popconfirm onConfirm={() => handleAudit(record.id, false)} title="确定要驳回吗?">
                <Button className="px-0" type="link" danger>
                  驳回
                </Button>
              </Popconfirm>
            </Space>
          );
        },
      },
    ],
    [form, handleAudit],
  );

  return (
    <div className="p-4 flex flex-col gap-5">
      <Modal
        maskClosable={false}
        cancelText="取消"
        confirmLoading={loading}
        okText="确定"
        onCancel={() => setPassModalVisible(false)}
        onOk={() => form.submit()}
        open={passModalVisible}
        title={`确定要通过 ${form.getFieldValue('full_name')} 的注册申请吗?`}
        width="50vw"
      >
        <Form
          className="p-4"
          disabled
          form={form}
          labelCol={{ span: 8 }}
          onFinish={() => handleAudit(form.getFieldValue('id'), true)}
          wrapperCol={{ span: 16 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="姓名" name="full_name" required>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="角色"
                name="roles"
                rules={[
                  { required: true, message: '请选择角色' },
                  { max: 1, type: 'array', message: '最多选择一个角色' },
                ]}
              >
                <Select
                  allowClear
                  mode="tags"
                  onChange={(value) => {
                    if (value.length > 1) {
                      form.setFieldsValue({ roles: [value[value.length - 1]] });
                    }
                  }}
                  options={ROLES_OPTIONS[currentUserRole]}
                  placeholder="请选择角色"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={12}>
              <Form.Item label="手机号" name="mobile" required>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="邮箱" name="email">
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      <div className="font-semibold">注册申请管理</div>

      <Radio.Group
        buttonStyle="solid"
        onChange={(event) => setTableType(event.target.value)}
        optionType="button"
        value={tableType}
      >
        <Radio value="全部">全部</Radio>
        <Radio value="待审核">待审核</Radio>
        <Radio value="已审核">已审核</Radio>
      </Radio.Group>

      <Table
        columns={columns}
        dataSource={registerList}
        loading={loading}
        pagination={false}
        rowKey="id"
        size="small"
      />
    </div>
  );
}
