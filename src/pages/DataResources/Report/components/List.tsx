import { ReactComponent as CenterIcon } from '@/assets/center_icon.svg';
import { ReactComponent as SensitiveIcon } from '@/assets/sensitive_icon.svg';
import { ReactComponent as ShareIcon } from '@/assets/share_icon.svg';
import { DataList } from '@/components/DataList';
import { approval, ListData, ListItem, release } from '@/services/dataResources/reportApi';
import { Button, Col, Form, Input, message, Modal, Row, Tag } from 'antd';
import { useRef, useState } from 'react';
import { history } from 'umi';
import { Approval } from './Approval';
import { ApprovalDetail } from './ApprovalDetail';

interface ReportListProps {
  data: ListData['list'];
  total?: number;
  type: string;
  onPaginationChange?: (page: number, pageSize: number) => void;
  refreshData: () => void;
}

// can_release 发布
// can_view_resource 资源详情
// can_update 修订申请
// can_view_approval 审批详情
type operations = 'can_release' | 'can_view_resource' | 'can_update' | 'can_view_approval';

interface StatusConfig {
  textColor: string;
  bgColor: string;
  desc?: string;
  operations: operations[];
}

const statusConfig: Record<number | string, StatusConfig> = {
  待拥有方审批: {
    textColor: 'rgba(0, 47, 165, 1)',
    bgColor: 'rgba(0, 47, 165, 0.05)',
    operations: ['can_release', 'can_view_approval'],
  },
  已通过: {
    textColor: 'rgba(4, 136, 79, 1)',
    bgColor: 'rgba(9, 200, 44, 0.09)',
    operations: ['can_release', 'can_view_approval'],
  },
  已发布: {
    textColor: 'rgba(4, 136, 79, 1)',
    bgColor: 'rgba(9, 200, 44, 0.09)',
    operations: ['can_update', 'can_view_approval'],
  },
  待管理方审批: {
    textColor: 'rgba(186, 164, 0, 1)',
    bgColor: 'rgba(186, 164, 0, 0.07)',
    operations: ['can_update', 'can_view_approval'],
  },
  已驳回: {
    textColor: 'rgba(230, 14, 18, 1)',
    bgColor: 'rgba(230, 14, 18, 0.06)',
    operations: ['can_update', 'can_view_approval'],
  },
  未通过: {
    textColor: 'rgba(230, 14, 18, 1)',
    bgColor: 'rgba(230, 14, 18, 0.06)',
    operations: ['can_update', 'can_view_approval'],
  },
};
export const ReportList = ({
  data,
  onPaginationChange,
  total,
  refreshData,
  type,
}: ReportListProps) => {
  const [showApproval, setShowApproval] = useState(false);
  const [showApprovalDetail, setShowApprovalDetail] = useState(false);
  const [showRelease, setShowRelease] = useState(false);
  const [currentDetail, setCurrentDetail] = useState<ListItem>();
  const approvalRef = useRef<{ validate: () => Promise<{ pass: boolean; advice: string }> }>(null);
  const [approvalSubmiting, setApprovalSubmiting] = useState(false);

  const [canApprovalDetail, setCanApprovalDetail] = useState(false);

  // 审批详情
  const handleApprovalDetailShow = (detail: ListItem) => {
    setCurrentDetail(detail);
    setShowApprovalDetail(true);
  };

  const handleReleaseShow = (detail: ListItem) => {
    setCurrentDetail(detail);
    setShowRelease(true);
  };

  // 审批
  const handleApprovalShow = (detail: ListItem) => {
    setCurrentDetail(detail);
    setShowApproval(true);
  };

  const handleRelease = async (id?: string) => {
    if (!id) return;
    const hideLoading = message.loading('发布中...', 0);
    try {
      const response = await release({ id });
      if (response.code === 200001) {
        refreshData();
        message.success('发布成功');
        setShowRelease(false);
      } else {
        message.error(`发布失败: ${response.message}`);
      }
    } finally {
      hideLoading();
    }
  };

  const handleApproval = async () => {
    if (!approvalRef?.current?.validate) return;
    if (!currentDetail || !currentDetail.id) return;
    setApprovalSubmiting(true);
    try {
      const values = await approvalRef.current.validate();
      const response = await approval(currentDetail.id, {
        advice: values.advice,
        pass: values.pass,
      });
      if (response.code === 200001) {
        message.success('审批成功');
        setShowApproval(false);
        refreshData();
      } else {
        message.error(`审批失败: ${response.message}`);
      }
    } catch (error) {
    } finally {
      setApprovalSubmiting(false);
    }
  };

  return (
    <>
      <DataList>
        {data.map((item) => {
          const config = statusConfig[item.status_str];
          return (
            <DataList.Item key={item.id}>
              <DataList.Header extra={item.approval_status_str}>
                {item.name}
                <Tag
                  className="h-fit"
                  bordered={false}
                  style={{ color: config.textColor, backgroundColor: config.bgColor }}
                >
                  {item.status_str}
                </Tag>
              </DataList.Header>
              <DataList.Content>
                <DataList.Description>数据资源序列编号：{item.num}</DataList.Description>
              </DataList.Content>
              <DataList.Footer
                extra={
                  <>
                    {item?.operations?.can_release && (
                      <Button type="link" className="px-0" onClick={() => handleReleaseShow(item)}>
                        发布
                      </Button>
                    )}
                    {item?.operations?.can_view_resource && (
                      <Button
                        type="link"
                        className="px-0"
                        onClick={() => history.push(`/dataResources/list/detail/${item.id}`)}
                      >
                        资源详情
                      </Button>
                    )}
                    {item?.operations?.can_update && (
                      <Button
                        type="link"
                        className="px-0"
                        onClick={() => history.push(`/dataResources/report/apply?id=${item.id}`)}
                      >
                        修订申请
                      </Button>
                    )}
                    {type === '1' && item.operations?.can_approve && (
                      <Button type="link" className="px-0" onClick={() => handleApprovalShow(item)}>
                        处理审批
                      </Button>
                    )}
                    {item?.operations?.can_view_approval && (
                      <Button
                        type="link"
                        className="px-0"
                        onClick={() => handleApprovalDetailShow(item)}
                      >
                        审批详情
                      </Button>
                    )}
                  </>
                }
              >
                {item.owner_dept && (
                  <div className="flex items-center gap-1 text-sm">
                    <CenterIcon width="20px" height="20px" />
                    {item.owner_dept}
                  </div>
                )}
                <div className="flex items-center gap-1 text-sm">
                  <ShareIcon width="20px" height="20px" />
                  {item.share_type}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  <SensitiveIcon width="20px" height="20px" />
                  {item.security_level}
                </div>
              </DataList.Footer>
            </DataList.Item>
          );
        })}
        <DataList.Pagination
          total={total}
          onChange={(page, pageSize) => {
            onPaginationChange?.(page, pageSize);
          }}
        />
      </DataList>
      <Modal
        maskClosable={false}
        title="资源上报申请审批"
        open={showApproval}
        width="50vw"
        onCancel={() => setShowApproval(false)}
        footer={[
          <div key="footer" className="text-center">
            <Button type="primary" onClick={handleApproval} loading={approvalSubmiting}>
              确认
            </Button>
          </div>,
        ]}
      >
        <Approval id={currentDetail?.id} ref={approvalRef} />
      </Modal>
      <Modal
        maskClosable={false}
        title="审批详情"
        width="50vw"
        open={showApprovalDetail}
        onCancel={() => setShowApprovalDetail(false)}
        footer={[
          <div key="footer" className="text-center">
            <Button onClick={() => setShowApprovalDetail(false)}>返回</Button>
            {canApprovalDetail && (
              <Button
                type="primary"
                className="ml-2"
                onClick={() => history.push(`/dataResources/list/detail/${currentDetail?.id}`)}
              >
                修订申请
              </Button>
            )}
          </div>,
        ]}
      >
        <ApprovalDetail id={currentDetail?.id} setCanApprovalDetail={setCanApprovalDetail} />
      </Modal>
      <Modal
        maskClosable={false}
        title="资源发布"
        open={showRelease}
        width="50vw"
        onCancel={() => setShowRelease(false)}
        footer={[
          <div className="text-center" key="footer">
            <Button onClick={() => setShowRelease(false)}>取消</Button>
            <Button
              className="ml-2"
              type="primary"
              onClick={() => handleRelease(currentDetail?.id)}
            >
              确定发布
            </Button>
          </div>,
        ]}
      >
        <Form labelCol={{ flex: '140px' }} labelWrap>
          <Row gutter={8}>
            <Col span={24}>
              <Form.Item label="数据资源中文名称">
                <Input disabled value={currentDetail?.name} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="拥有公司及部门">
                <Input disabled value={currentDetail?.owner_dept} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="来源系统或报表名称">
                <Input disabled value={currentDetail?.source_name} />
              </Form.Item>
            </Col>
          </Row>
          <div className="text-center">是否确定发布该数据资源？</div>
        </Form>
      </Modal>
    </>
  );
};
