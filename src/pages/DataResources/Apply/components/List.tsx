import { ReactComponent as CenterIcon } from '@/assets/center_icon.svg';
import { ReactComponent as SensitiveIcon } from '@/assets/sensitive_icon.svg';
import { ReactComponent as ShareIcon } from '@/assets/share_icon.svg';
import { DataList } from '@/components/DataList';
import {
  ResourceUseApplicationListOperations,
  ResourceUseListData,
  ResourceUseListItem,
  ResourceUseVettingListOperations,
} from '@/services/dataResources/applyApi';
import { Button, Modal, Tag } from 'antd';
import { useState } from 'react';
import { history } from 'umi';
import { VettingDetail } from './VettingDetail';
import { VettingProcess } from './VettingProcess';

interface ApplyListProps {
  data: ResourceUseListData['list'];
  onPaginationChange?: (page: number, pageSize: number) => void;
  // refreshData: () => void;
  total?: number;
  type: string;
}

const statusConfig = {
  待使用方审批: {
    textColor: 'rgba(0, 47, 165, 1)',
    bgColor: 'rgba(0, 47, 165, 0.05)',
    operations: ['can_release', 'can_view_approval'],
  },
  已通过: {
    textColor: 'rgba(4, 136, 79, 1)',
    bgColor: 'rgba(9, 200, 44, 0.09)',
    operations: ['can_release', 'can_view_approval'],
  },
  待拥有方审批: {
    textColor: 'rgba(4, 136, 79, 1)',
    bgColor: 'rgba(9, 200, 44, 0.09)',
    operations: ['can_update', 'can_view_approval'],
  },
  待管理方审批: {
    textColor: 'rgba(186, 164, 0, 1)',
    bgColor: 'rgba(186, 164, 0, 0.07)',
    operations: ['can_update', 'can_view_approval'],
  },
  已驳回: {
    textColor: 'rgba(230, 14, 18, 1)',
    bgColor: 'rgba(230, 14, 18, 0.06)',
    operations: ['can_update', 'can_view_approval'],
  },
  未通过: {
    textColor: 'rgba(230, 14, 18, 1)',
    bgColor: 'rgba(230, 14, 18, 0.06)',
    operations: ['can_update', 'can_view_approval'],
  },
};

export const ApplyList = ({
  data,
  onPaginationChange,
  // refreshData,
  total,
  type,
}: ApplyListProps) => {
  const [currentItem, setCurrentItem] = useState<ResourceUseListItem>();

  const [isShowVettingDetail, setIsShowVettingDetail] = useState(false);
  const [isShowVettingProcess, setIsShowVettingProcess] = useState(false);
  const [isShowApplicationModify, setIsShowApplicationModify] = useState(false);

  const handleVettingDetailClick = (item: ResourceUseListItem) => {
    setCurrentItem(item);
    setIsShowVettingDetail(true);
  };

  const handleVettingProcessClick = (item: ResourceUseListItem) => {
    setCurrentItem(item);
    setIsShowVettingProcess(true);
  };

  const handleApplicationModifyClick = (item: ResourceUseListItem) => {
    setCurrentItem(item);
    setIsShowApplicationModify(true);
  };

  return (
    <>
      <Modal
        maskClosable={false}
        onCancel={() => setIsShowVettingDetail(false)}
        onOk={() => {}}
        open={isShowVettingDetail}
        title="审批详情"
        width="50vw"
        footer={[
          <div key="footer" className="text-right">
            <Button type="primary" onClick={() => setIsShowVettingDetail(false)}>
              确认
            </Button>
          </div>,
        ]}
      >
        <VettingDetail id={currentItem?.apply_id} />
      </Modal>

      <Modal
        maskClosable={false}
        onCancel={() => setIsShowVettingProcess(false)}
        open={isShowVettingProcess}
        title="资源使用申请审批"
        width="50vw"
        footer={null}
      >
        <VettingProcess
          id={currentItem?.apply_id}
          setIsShowModal={setIsShowVettingProcess}
          type="1"
        />
      </Modal>

      <Modal
        maskClosable={false}
        onCancel={() => setIsShowApplicationModify(false)}
        open={isShowApplicationModify}
        title="资源使用申请修改"
        width="50vw"
        footer={null}
      >
        <VettingProcess
          id={currentItem?.apply_id}
          setIsShowModal={setIsShowApplicationModify}
          type="0"
        />
      </Modal>

      <DataList>
        {data.length > 0 &&
          data.map((item) => {
            const config = statusConfig[item.status_str as keyof typeof statusConfig];
            return (
              <DataList.Item key={item?.id}>
                <DataList.Header extra={item?.approval_status_str}>
                  {item?.name}
                  <Tag
                    className="h-fit"
                    bordered={false}
                    style={{ color: config?.textColor, backgroundColor: config?.bgColor }}
                  >
                    {item?.status_str}
                  </Tag>
                </DataList.Header>
                <DataList.Content>
                  <DataList.Description>数据资源序列编号：{item?.num}</DataList.Description>
                </DataList.Content>
                <DataList.Footer
                  extra={
                    <>
                      {/* {type === '0' &&
                        (item?.operations as ResourceUseApplicationListOperations)
                          ?.can_supplement && (
                          <Button type="link" className="px-0">
                            补充资料
                          </Button>
                        )} */}
                      {item?.operations?.can_view_resource && (
                        <Button
                          type="link"
                          className="px-0"
                          onClick={() => history.push(`/dataResources/list/detail/${item.id}`)}
                        >
                          资源详情
                        </Button>
                      )}
                      {type === '0' &&
                        (item?.operations as ResourceUseApplicationListOperations)?.can_update && (
                          <Button
                            type="link"
                            className="px-0"
                            onClick={() => handleApplicationModifyClick(item)}
                          >
                            修订申请
                          </Button>
                        )}
                      {type === '1' &&
                        (item?.operations as ResourceUseVettingListOperations)?.can_approve && (
                          <Button
                            type="link"
                            className="px-0"
                            onClick={() => handleVettingProcessClick(item)}
                          >
                            处理审批
                          </Button>
                        )}
                      {item?.operations?.can_view_approval && (
                        <Button
                          type="link"
                          className="px-0"
                          onClick={() => handleVettingDetailClick(item)}
                        >
                          审批详情
                        </Button>
                      )}
                    </>
                  }
                >
                  {item?.owner_dept && (
                    <div className="flex items-center gap-1 text-sm">
                      <CenterIcon width="20px" height="20px" />
                      {item?.owner_dept}
                    </div>
                  )}
                  {item?.owner_dept && (
                    <div className="flex items-center gap-1 text-sm">
                      <CenterIcon width="20px" height="20px" />
                      {item?.head_name}
                    </div>
                  )}
                  <div className="flex items-center gap-1 text-sm">
                    <ShareIcon width="20px" height="20px" />
                    {item?.share_type}
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <SensitiveIcon width="20px" height="20px" />
                    {item?.sensitive_desc}
                  </div>
                </DataList.Footer>
              </DataList.Item>
            );
          })}
        <DataList.Pagination
          total={total}
          onChange={(page, pageSize) => {
            onPaginationChange?.(page, pageSize);
          }}
        />
      </DataList>
    </>
  );
};
