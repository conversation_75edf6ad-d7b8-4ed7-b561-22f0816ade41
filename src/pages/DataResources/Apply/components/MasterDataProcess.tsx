import { SubTitle } from '@/components/SubTitle';
import {
  getApplyMasterDataApprovalPage,
  getApplyMasterDataDetail,
  postApplyMasterDataApproval,
  ResourceUseResponse,
} from '@/services/dataResources/applyApi';
import { getOrgsTree } from '@/services/dataResources/orgsApi';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, message, Radio, Row, Select, Spin } from 'antd';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { history } from 'umi';

const { TextArea } = Input;

export const MasterDataProcess = ({
  id,
  setIsShowModal,
  type,
}: {
  id?: string;
  setIsShowModal?: Dispatch<SetStateAction<boolean>>;
  type: string;
}) => {
  const rules = { requiredField: [{ required: true, message: '必填项不能为空' }] };

  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [radioApproval, setRadioApproval] = useState(true);
  //主数据 detail
  const [masterData, setMasterData] = useState<any>({});
  //组织架构
  const [orgSelectOptions, setOrgSelectOptions] = useState<any[]>([]);

  const flattenOrgs = (orgs: any[]) => {
    const result: any[] = [];
    const recurse = (nodes: any[]) => {
      nodes.forEach((node) => {
        result.push(node);
        if (node.children) {
          recurse(node.children);
        }
      });
    };
    recurse(orgs);
    return result;
  };

  //获取组织架构
  const getOrgs = async () => {
    try {
      const res = await getOrgsTree();

      if (res.code === 200001) {
        const orgsData = flattenOrgs(res.data).map((org) => ({
          value: org.id,
          label: org.name,
        }));
        setOrgSelectOptions(orgsData);
        return;
      }

      message.error(res.message);
    } catch (error) {
      message.error(JSON.stringify(error));
    }
  };

  //主数据审批
  const onMasterVettingProcessFinish = async () => {
    if (!id) return;
    setLoading(true);
    const vettingAdvice = form.getFieldsValue([
      'advice',
      // 'approved_at',
      // 'approver',
      // 'approver_position',
      'pass',
    ]);
    if (!vettingAdvice.pass && !vettingAdvice.advice) {
      message.error('请填写审批意见');
      return;
    }
    try {
      const response: ResourceUseResponse<null> = await postApplyMasterDataApproval(
        id,
        vettingAdvice,
      );
      if (response.code !== 200001) {
        message.error(response.message);
        return;
      }
      message.success('操作成功');
      setIsShowModal?.(false);
      history.go(0);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    (async () => {
      setLoading(true);
      try {
        let response;
        if (type === '0') {
          response = await getApplyMasterDataDetail(id);
        } else {
          response = await getApplyMasterDataApprovalPage(id);
        }
        if (response.code !== 200001) {
          message.error(response.message);
          return;
        }
        setMasterData(response.data);
        form.setFieldsValue({
          ...response.data,
          macs: response.data.macs.join('\n'),
          use_range: response.data.use_start_at + '~' + response.data.use_end_at,
        });
      } catch (error) {
        message.error(JSON.stringify(error));
      } finally {
        setLoading(false);
      }
    })();
  }, [id]);
  useEffect(() => {
    getOrgs();
  }, []);

  return (
    <Spin spinning={loading}>
      <Form
        labelCol={{ flex: '180px' }}
        form={form}
        labelAlign="right"
        onFinish={onMasterVettingProcessFinish}
      >
        <SubTitle title="申请信息" />
        <div className="max-w-3xl mx-auto">
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="需求描述"
                name="req_desc"
                rules={[{ required: true, message: '需求描述为必填' }]}
              >
                <Input.TextArea disabled style={{ height: '80px' }} />
              </Form.Item>
            </Col>
          </Row>
          {masterData.major_data_name !== '会计科目' && (
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="数据所属组织架构"
                  name="approval_company_id"
                  rules={[{ required: true, message: '数据所属组织架构为必填' }]}
                >
                  <Select options={orgSelectOptions} showSearch optionFilterProp="label" disabled />
                </Form.Item>
              </Col>
            </Row>
          )}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="数据使用合规性声明"
                tooltip={{
                  title: '请仔细阅读数据使用合规性声明',
                  icon: <InfoCircleOutlined />,
                }}
                wrapperCol={{ offset: 1 }}
                name="is_agree"
                rules={[{ required: true, message: '是否同意数据使用合规性声明为必填' }]}
              >
                <Radio.Group disabled>
                  <Radio value={true}>同意</Radio>
                  <Radio value={false}>不同意</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="数据安全措施"
                name="security_step"
                rules={[{ required: true, message: '数据安全措施为必填' }]}
              >
                <Input.TextArea disabled style={{ height: '80px' }} showCount maxLength={100} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="访问源硬件地址"
                name="macs"
                rules={[{ required: true, message: '访问源硬件地址为必填' }]}
              >
                <Input.TextArea
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  placeholder="请输入访问源硬件地址，每行为一个地址，多个地址用 Enter 分隔"
                  disabled
                />
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={24}>
              <Form.Item label="备注" name="remark">
                <Input.TextArea
                  disabled
                  style={{
                    height: '60px',
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>
        <>
          <SubTitle title="审批意见" />
          <div className="max-w-3xl mx-auto">
            <Row gutter={12}>
              <Col span={24}>
                <Form.Item label="审批意见" name="pass" rules={rules.requiredField}>
                  <Radio.Group onChange={(value) => setRadioApproval(value.target.value)}>
                    <Radio value={true}>通过</Radio>
                    <Radio value={false}>驳回</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={12}>
              <Col span={24}>
                {!radioApproval && (
                  <Form.Item label="反馈意见" name="advice">
                    <TextArea />
                  </Form.Item>
                )}
              </Col>
            </Row>
          </div>
        </>
        <div className="text-center">
          <Button
            className="mr-2"
            onClick={() => {
              setIsShowModal?.(false);
            }}
          >
            取消
          </Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            确认
          </Button>
        </div>
      </Form>
    </Spin>
  );
};
